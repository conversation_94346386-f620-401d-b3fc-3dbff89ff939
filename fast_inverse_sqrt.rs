/// Fast inverse square root implementations in Rust
/// Based on the famous Quake algorithm and modern optimizations

use std::f32;
use std::f64;

/// Classic Quake-style fast inverse square root for f32
/// Returns 1/sqrt(x) with good approximation
#[inline]
pub fn fast_inv_sqrt_f32(x: f32) -> f32 {
    let i = x.to_bits();
    let i = 0x5f3759df - (i >> 1); // Magic number from Quake
    let y = f32::from_bits(i);
    
    // One Newton-Raphson iteration for better accuracy
    let x2 = x * 0.5;
    y * (1.5 - x2 * y * y)
}

/// Improved fast inverse square root with better magic constant
#[inline]
pub fn fast_inv_sqrt_improved_f32(x: f32) -> f32 {
    let i = x.to_bits();
    let i = 0x5f375a86 - (i >> 1); // Improved magic number
    let y = f32::from_bits(i);
    
    // One Newton-Raphson iteration
    let x2 = x * 0.5;
    y * (1.5 - x2 * y * y)
}

/// High precision version with two Newton-Raphson iterations
#[inline]
pub fn fast_inv_sqrt_precise_f32(x: f32) -> f32 {
    let i = x.to_bits();
    let i = 0x5f375a86 - (i >> 1);
    let mut y = f32::from_bits(i);
    
    let x2 = x * 0.5;
    
    // First Newton-Raphson iteration
    y = y * (1.5 - x2 * y * y);
    
    // Second iteration for higher precision
    y * (1.5 - x2 * y * y)
}

/// Fast inverse square root for f64 (double precision)
#[inline]
pub fn fast_inv_sqrt_f64(x: f64) -> f64 {
    let i = x.to_bits();
    let i = 0x5fe6eb50c7b537a9u64 - (i >> 1); // Magic number for f64
    let y = f64::from_bits(i);
    
    // One Newton-Raphson iteration
    let x2 = x * 0.5;
    y * (1.5 - x2 * y * y)
}

/// Ultra-fast version without Newton-Raphson (less accurate)
#[inline]
pub fn ultra_fast_inv_sqrt_f32(x: f32) -> f32 {
    let i = x.to_bits();
    let i = 0x5f3759df - (i >> 1);
    f32::from_bits(i)
}

/// SIMD-optimized version using std::simd (requires nightly Rust)
#[cfg(feature = "simd")]
use std::simd::{f32x4, u32x4};

#[cfg(feature = "simd")]
#[inline]
pub fn fast_inv_sqrt_simd(x: f32x4) -> f32x4 {
    let magic = u32x4::splat(0x5f375a86);
    let half = f32x4::splat(0.5);
    let one_half = f32x4::splat(1.5);
    
    let i = x.to_bits();
    let i = magic - (i >> 1);
    let y = f32x4::from_bits(i);
    
    let x2 = x * half;
    y * (one_half - x2 * y * y)
}

/// Modern approach using hardware reciprocal square root estimate + refinement
/// This leverages CPU instructions when available
#[inline]
pub fn hardware_assisted_inv_sqrt_f32(x: f32) -> f32 {
    // On x86 with SSE, this might use RSQRTSS instruction
    // Fallback to fast approximation if not available
    #[cfg(target_arch = "x86_64")]
    {
        #[cfg(target_feature = "sse")]
        unsafe {
            use std::arch::x86_64::*;
            let x_vec = _mm_set_ss(x);
            let result = _mm_rsqrt_ss(x_vec);
            let result = _mm_cvtss_f32(result);
            
            // One Newton-Raphson refinement
            let x2 = x * 0.5;
            result * (1.5 - x2 * result * result)
        }
        #[cfg(not(target_feature = "sse"))]
        {
            fast_inv_sqrt_improved_f32(x)
        }
    }
    #[cfg(not(target_arch = "x86_64"))]
    {
        fast_inv_sqrt_improved_f32(x)
    }
}

/// Convenience function to get square root using inverse square root
#[inline]
pub fn fast_sqrt_f32(x: f32) -> f32 {
    x * fast_inv_sqrt_improved_f32(x)
}

/// Branch-free version for better performance in tight loops
#[inline]
pub fn fast_inv_sqrt_branchfree_f32(x: f32) -> f32 {
    // Handle edge cases without branches
    let is_zero = (x == 0.0) as u32;
    let is_inf = x.is_infinite() as u32;
    
    let i = x.to_bits();
    let i = 0x5f375a86 - (i >> 1);
    let y = f32::from_bits(i);
    
    let x2 = x * 0.5;
    let result = y * (1.5 - x2 * y * y);
    
    // Return 0 for zero input, 0 for infinite input
    let mask = !(is_zero | is_inf);
    f32::from_bits(result.to_bits() & (mask.wrapping_neg()))
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_fast_inv_sqrt_accuracy() {
        let test_values = [1.0, 4.0, 9.0, 16.0, 25.0, 0.25, 0.01, 100.0];
        
        for &x in &test_values {
            let expected = 1.0 / x.sqrt();
            let fast_result = fast_inv_sqrt_improved_f32(x);
            let error = (fast_result - expected).abs() / expected;
            
            println!("x: {}, expected: {}, fast: {}, error: {:.6}%", 
                     x, expected, fast_result, error * 100.0);
            
            // Should be accurate to within 0.2% for most values
            assert!(error < 0.002, "Error too large for x={}: {}%", x, error * 100.0);
        }
    }
    
    #[test]
    fn test_performance_comparison() {
        let x = 42.0f32;
        let iterations = 1_000_000;
        
        // Time the different implementations
        let start = std::time::Instant::now();
        for _ in 0..iterations {
            std::hint::black_box(1.0 / std::hint::black_box(x).sqrt());
        }
        let std_time = start.elapsed();
        
        let start = std::time::Instant::now();
        for _ in 0..iterations {
            std::hint::black_box(fast_inv_sqrt_improved_f32(std::hint::black_box(x)));
        }
        let fast_time = start.elapsed();
        
        println!("Standard: {:?}, Fast: {:?}, Speedup: {:.2}x", 
                 std_time, fast_time, std_time.as_nanos() as f64 / fast_time.as_nanos() as f64);
    }
}

/// Example usage and benchmarking
pub fn demo_fast_inverse_sqrt() {
    let x = 42.0f32;

    println!("Input: {}", x);
    println!("Standard 1/sqrt(x): {}", 1.0 / x.sqrt());
    println!("Fast inv sqrt: {}", fast_inv_sqrt_f32(x));
    println!("Improved fast inv sqrt: {}", fast_inv_sqrt_improved_f32(x));
    println!("Precise fast inv sqrt: {}", fast_inv_sqrt_precise_f32(x));
    println!("Ultra fast (no refinement): {}", ultra_fast_inv_sqrt_f32(x));

    // Demonstrate square root calculation
    println!("\nSquare root via fast inverse:");
    println!("sqrt({}) = {}", x, fast_sqrt_f32(x));
    println!("Standard sqrt({}) = {}", x, x.sqrt());
}
