use criterion::{black_box, criterion_group, criterion_main, Criterion};
use verturion::fast_inverse_sqrt::*;

fn benchmark_inverse_sqrt(c: &mut Criterion) {
    let test_value = 42.0f32;
    
    c.bench_function("standard_inv_sqrt", |b| {
        b.iter(|| 1.0 / black_box(test_value).sqrt())
    });
    
    c.bench_function("fast_inv_sqrt_original", |b| {
        b.iter(|| fast_inv_sqrt_f32(black_box(test_value)))
    });
    
    c.bench_function("fast_inv_sqrt_improved", |b| {
        b.iter(|| fast_inv_sqrt_improved_f32(black_box(test_value)))
    });
    
    c.bench_function("fast_inv_sqrt_precise", |b| {
        b.iter(|| fast_inv_sqrt_precise_f32(black_box(test_value)))
    });
    
    c.bench_function("ultra_fast_inv_sqrt", |b| {
        b.iter(|| ultra_fast_inv_sqrt_f32(black_box(test_value)))
    });
    
    c.bench_function("hardware_assisted_inv_sqrt", |b| {
        b.iter(|| hardware_assisted_inv_sqrt_f32(black_box(test_value)))
    });
}

fn benchmark_sqrt_via_inv_sqrt(c: &mut Criterion) {
    let test_value = 42.0f32;
    
    c.bench_function("standard_sqrt", |b| {
        b.iter(|| black_box(test_value).sqrt())
    });
    
    c.bench_function("sqrt_via_fast_inv_sqrt", |b| {
        b.iter(|| fast_sqrt_f32(black_box(test_value)))
    });
}

fn benchmark_array_operations(c: &mut Criterion) {
    let test_array: Vec<f32> = (0..1000).map(|i| (i as f32) + 1.0).collect();
    
    c.bench_function("array_standard_inv_sqrt", |b| {
        b.iter(|| {
            let array = black_box(&test_array);
            array.iter().map(|&x| 1.0 / x.sqrt()).collect::<Vec<f32>>()
        })
    });
    
    c.bench_function("array_fast_inv_sqrt", |b| {
        b.iter(|| {
            let array = black_box(&test_array);
            array.iter().map(|&x| fast_inv_sqrt_improved_f32(x)).collect::<Vec<f32>>()
        })
    });
}

criterion_group!(
    benches, 
    benchmark_inverse_sqrt, 
    benchmark_sqrt_via_inv_sqrt,
    benchmark_array_operations
);
criterion_main!(benches);
