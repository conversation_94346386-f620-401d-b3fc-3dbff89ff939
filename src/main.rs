use verturion::core::Math;

mod fast_inverse_sqrt;
use fast_inverse_sqrt::*;

fn main() {
    println!("\n=== Performance Comparison ===");
    performance_test();
}

fn performance_test() {
    let test_values: [f32; 8] = [1.0, 4.0, 9.0, 16.0, 25.0, 42.0, 100.0, 1000.0];

    println!("Value\t\tStandard\t\tFast\t\t\tError %");
    println!("-----\t\t--------\t\t--------\t\t-------");

    for &x in &test_values {
        let standard = x.sqrt();
        let fast = Math::sqrt(x);
        let error = (fast - standard).abs() / standard * 100.0;

        println!(
            "{:.1}\t\t{:.8}\t\t{:.8}\t\t{:.1}%",
            x, standard, fast, error
        );
    }
}
