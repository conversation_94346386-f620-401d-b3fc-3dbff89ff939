pub mod static_vector2;
pub mod vector2;

pub use static_vector2::StaticVector2;
pub use vector2::Vector2;

pub struct Math {}

impl Math {
    pub const fn sqrt(value: f32) -> f32 {
        //    x * 0.5
        //        / (x * 0.5
        //            / ((-80.0 * x - 64.0) / (x * (x + 24.0) + 16.0) + 0.0625 * x - 4.0 / (x + 4.0)
        //                + 5.25)
        //            + ((-80.0 * x - 64.0) / (x * (x + 24.0) + 16.0) + 0.0625 * x - 4.0 / (x + 4.0)
        //                + 5.25)
        //                * 0.5)
        //        + (x * 0.5
        //            / ((-80.0 * x - 64.0) / (x * (x + 24.0) + 16.0) + 0.0625 * x - 4.0 / (x + 4.0)
        //                + 5.25)
        //            + ((-80.0 * x - 64.0) / (x * (x + 24.0) + 16.0) + 0.0625 * x - 4.0 / (x + 4.0)
        //                + 5.25)
        //                * 0.5)
        //            * 0.5

        //12,5 × (12,5 × 0,8) ÷ 25

        value * 0.5 * (value * 0.5 * 0.8) / value
    }
}
