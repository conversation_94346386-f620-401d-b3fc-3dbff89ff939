pub mod static_vector2;
pub mod vector2;

pub use static_vector2::StaticVector2;
pub use vector2::Vector2;

pub struct Math {}

impl Math {
    pub const TAU: f32 = std::f32::consts::TAU;
    pub const PI: f32 = std::f32::consts::PI;
    pub const PI_HALF: f32 = std::f32::consts::FRAC_PI_2;
    pub const INF: f32 = std::f32::INFINITY;
    pub const NEG_INF: f32 = std::f32::NEG_INFINITY;
    pub const NAN: f32 = std::f32::NAN;

    pub const fn fast_inverse_sqrt(value: f32) -> f32 {
        let x = value.to_bits();
        let y = f32::from_bits(1597100000u32 - (x >> 1));
        let x_half = f32::from_bits(x - 0x800000);

        y * (1.5f32 - x_half * y * y)
    }

    /// ## A square root operation that gets a close estimate based on the inverse square root of the number.
    /// By using a fast inverse square root function (modified from Quake's implementation), it gets the square root by
    /// multiplying the value by the inverse square root of the value, resulting in getting the actual (estimated) square
    /// root value.
    ///
    /// The value has less, than 0.001% error rate, however if this accuracy rating is acceptable for scenarios, then this
    /// function is more preferable due to it's performance.
    ///
    /// Calculation: value * inverse_sqrt(value)
    pub const fn fast_sqrt(value: f32) -> f32 {
        value * Math::fast_inverse_sqrt(value)
    }

    pub const fn sqrt(value: f32) -> f32 {
        value * 0.5
            / (value * 0.5
                / ((-80.0 * value - 64.0) / (value * (value + 24.0) + 16.0) + 0.0625 * value
                    - 4.0 / (value + 4.0)
                    + 5.25)
                + ((-80.0 * value - 64.0) / (value * (value + 24.0) + 16.0) + 0.0625 * value
                    - 4.0 / (value + 4.0)
                    + 5.25)
                    * 0.5)
            + (value * 0.5
                / ((-80.0 * value - 64.0) / (value * (value + 24.0) + 16.0) + 0.0625 * value
                    - 4.0 / (value + 4.0)
                    + 5.25)
                + ((-80.0 * value - 64.0) / (value * (value + 24.0) + 16.0) + 0.0625 * value
                    - 4.0 / (value + 4.0)
                    + 5.25)
                    * 0.5)
                * 0.5
    }

    pub const fn inverse_sqrt(value: f32) -> f32 {
        1.0f32 / Math::sqrt(value)
    }

    pub const fn atan_custom(x: f32) -> f32 {
        // Constants for the polynomial approximation
        const A: f32 = 0.999999999999999;
        const B: f32 = -0.333333333333333;
        const C: f32 = 0.2;
        const D: f32 = -0.142857142857143;
        const E: f32 = 0.111111111111111;
        const F: f32 = -0.090909090909091;

        // Handle edge cases
        if x.is_nan() {
            return Math::NAN;
        } else if x == Math::INF {
            return Math::PI_HALF;
        } else if x == Math::NEG_INF {
            return -Math::PI_HALF;
        }

        // Use the polynomial approximation
        let x2 = x * x;
        let x3 = x2 * x2;
        let x4 = x3 * x2;
        let x5 = x3 * x3;
        let x6 = x5 * x2;
        let result = A + (B * x2 + (C * x3 + (D * x4 + (E * x5 + F * x6 * x))));

        // Adjust result based on the input value
        if x < 0.0 {
            -result
        } else {
            result
        }
    }

    pub const fn abs_custom(x: f32) -> f32 {
        f32::from_bits(x.to_bits() & 0x7FFFFFFF)
    }
}
